"use client";

import React, { useState, useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import { ColDef, GridApi, GridReadyEvent } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { Badge } from "~/component/ui/badge";

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  validRoamingTariffs: Array<{
    id: string;
    name: string;
    currentType: string;
    kwh: number;
    sessionFee: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string;
    validOus: Array<{
      id: string;
      name: string;
      code: string;
    }>;
  }>;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [rowData, setRowData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setRowData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  const TariffRenderer = ({ value }: { value: RoamingMatrixData["validRoamingTariffs"] }) => {
    if (!value || value.length === 0) {
      return <span className="text-gray-400">Keine gültigen Tarife</span>;
    }

    return (
      <div className="flex flex-wrap gap-1">
        {value.map((tariff) => (
          <Badge
            key={tariff.id}
            variant="outline"
            className="text-xs"
            title={`${tariff.name} - ${tariff.currentType} - ${tariff.kwh}€/kWh - ${tariff.sessionFee}€/Session`}
          >
            {tariff.name} ({tariff.currentType})
          </Badge>
        ))}
      </div>
    );
  };

  const CreditTariffRenderer = ({ value }: { value: RoamingMatrixData["validCreditTariffs"] }) => {
    if (!value || value.length === 0) {
      return <span className="text-gray-400">Keine Kredittarife</span>;
    }

    return (
      <div className="flex flex-wrap gap-1">
        {value.map((tariff) => (
          <Badge
            key={tariff.id}
            variant="secondary"
            className="text-xs"
            title={`${tariff.name} - ${tariff.powerType} - ${tariff.energyCredit}€ Energie, ${tariff.sessionCredit}€ Session`}
          >
            {tariff.name} ({tariff.powerType})
          </Badge>
        ))}
      </div>
    );
  };

  const OuRenderer = ({ value }: { value: RoamingMatrixData["ou"] }) => {
    if (!value) {
      return <span className="text-gray-400">Keine OU</span>;
    }
    return <span>{value.name} ({value.code})</span>;
  };

  const BooleanRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge variant={value ? "default" : "secondary"} className="text-xs">
        {value ? "Ja" : "Nein"}
      </Badge>
    );
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Name",
      field: "name",
      sortable: true,
      filter: true,
      pinned: "left",
      width: 200,
    },
    {
      headerName: "Firma",
      field: "companyName",
      sortable: true,
      filter: true,
      width: 200,
    },
    {
      headerName: "Kundennummer",
      field: "customerNumber",
      sortable: true,
      filter: true,
      width: 150,
    },
    {
      headerName: "Lieferantennummer",
      field: "supplierNumber",
      sortable: true,
      filter: true,
      width: 150,
    },
    {
      headerName: "OU",
      field: "ou",
      cellRenderer: OuRenderer,
      sortable: true,
      filter: true,
      width: 150,
    },
    {
      headerName: "CPO",
      field: "cpo",
      cellRenderer: BooleanRenderer,
      sortable: true,
      filter: true,
      width: 100,
    },
    {
      headerName: "Kein Roaming",
      field: "noRoaming",
      cellRenderer: BooleanRenderer,
      sortable: true,
      filter: true,
      width: 120,
    },
    {
      headerName: "Gültige Roaming-Tarife",
      field: "validRoamingTariffs",
      cellRenderer: TariffRenderer,
      sortable: false,
      filter: false,
      width: 300,
      autoHeight: true,
    },
    {
      headerName: "Gültige Kredit-Tarife",
      field: "validCreditTariffs",
      cellRenderer: CreditTariffRenderer,
      sortable: false,
      filter: false,
      width: 300,
      autoHeight: true,
    },
  ];

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const defaultColDef = {
    resizable: true,
    sortable: true,
    filter: true,
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="ag-theme-alpine" style={{ height: "600px", width: "100%" }}>
          <AgGridReact
            rowData={rowData}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            onGridReady={onGridReady}
            loading={loading}
            animateRows={true}
            rowHeight={60}
            headerHeight={40}
            suppressRowClickSelection={true}
            enableCellTextSelection={true}
            ensureDomOrder={true}
            sideBar={{
              toolPanels: [
                {
                  id: "columns",
                  labelDefault: "Columns",
                  labelKey: "columns",
                  iconKey: "columns",
                  toolPanel: "agColumnsToolPanel",
                },
                {
                  id: "filters",
                  labelDefault: "Filters",
                  labelKey: "filters",
                  iconKey: "filter",
                  toolPanel: "agFiltersToolPanel",
                },
              ],
              position: "right",
              defaultToolPanel: "",
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default RoamingMatrixPage;

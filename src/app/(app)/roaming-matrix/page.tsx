"use client";

import React, { useState, useEffect } from "react";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";
import { Chip } from "~/app/(app)/component/chip";

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  validRoamingTariffs: Array<{
    id: string;
    name: string;
    currentType: string;
    kwh: number;
    sessionFee: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string;
    validOus: Array<{
      id: string;
      name: string;
      code: string;
    }>;
  }>;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = (contactId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(contactId)) {
      newExpanded.delete(contactId);
    } else {
      newExpanded.add(contactId);
    }
    setExpandedItems(newExpanded);
  };

  const TariffDetails = ({ tariffs, type }: { tariffs: RoamingMatrixData["validRoamingTariffs"], type: "AC" | "DC" }) => {
    const filteredTariffs = tariffs.filter(tariff => tariff.currentType === type);

    if (filteredTariffs.length === 0) {
      return (
        <div className="text-gray-400 text-sm">
          Kein {type}-Tarif verfügbar
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {filteredTariffs.map((tariff) => (
          <div key={tariff.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">kWh-Preis:</span>
                <span className="ml-2 font-semibold">{tariff.kwh.toFixed(4)}€</span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">Session-Preis:</span>
                <span className="ml-2 font-semibold">{tariff.sessionFee?.toFixed(2) || '0.00'}€</span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">Min. Ladezeit:</span>
                <span className="ml-2">{tariff.minChargingTime}s</span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">Min. Energie:</span>
                <span className="ml-2">{tariff.minChargingEnergy}kWh</span>
              </div>
              {tariff.validOus && tariff.validOus.length > 0 && (
                <div className="col-span-2">
                  <span className="font-medium text-gray-600 dark:text-gray-300">Gültig für OUs:</span>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {tariff.validOus.map((ou) => (
                      <Chip
                        key={ou.id}
                        label={`${ou.name} (${ou.code})`}
                        className="bg-blue-100 text-blue-800 text-xs"
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const ContactItem = ({ contact }: { contact: RoamingMatrixData }) => {
    const isExpanded = expandedItems.has(contact.id);
    const acTariffs = contact.validRoamingTariffs.filter(t => t.currentType === "AC");
    const dcTariffs = contact.validRoamingTariffs.filter(t => t.currentType === "DC");

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-4">
        {/* Header - immer sichtbar */}
        <div
          className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          onClick={() => toggleExpanded(contact.id)}
        >
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {isExpanded ? (
                <FaChevronDown className="text-gray-400" />
              ) : (
                <FaChevronRight className="text-gray-400" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {contact.companyName || contact.name || "Unbekannter Contact"}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                {contact.customerNumber && (
                  <span>Kunde: {contact.customerNumber}</span>
                )}
                {contact.ou && (
                  <span>OU: {contact.ou.name} ({contact.ou.code})</span>
                )}
                <span className="flex items-center space-x-1">
                  <span>AC:</span>
                  <Chip
                    label={acTariffs.length.toString()}
                    className="bg-blue-100 text-blue-800 text-xs"
                  />
                </span>
                <span className="flex items-center space-x-1">
                  <span>DC:</span>
                  <Chip
                    label={dcTariffs.length.toString()}
                    className="bg-green-100 text-green-800 text-xs"
                  />
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && (
              <Chip label="CPO" className="bg-purple-100 text-purple-800 text-xs" />
            )}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-red-800 text-xs" />
            )}
          </div>
        </div>

        {/* Aufgeklappter Inhalt */}
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* AC Tarife */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-2">AC</span>
                  Wechselstrom-Tarife
                </h4>
                <TariffDetails tariffs={contact.validRoamingTariffs} type="AC" />
              </div>

              {/* DC Tarife */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-2">DC</span>
                  Gleichstrom-Tarife
                </h4>
                <TariffDetails tariffs={contact.validRoamingTariffs} type="DC" />
              </div>
            </div>

            {/* Kredit-Tarife falls vorhanden */}
            {contact.validCreditTariffs.length > 0 && (
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-medium mr-2">KREDIT</span>
                  Kredit-Tarife
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {contact.validCreditTariffs.map((tariff) => (
                    <div key={tariff.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-600 dark:text-gray-300">Typ:</span>
                          <span className="ml-2">{tariff.powerType}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600 dark:text-gray-300">Energie-Kredit:</span>
                          <span className="ml-2 font-semibold">{tariff.energyCredit}€</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600 dark:text-gray-300">Session-Kredit:</span>
                          <span className="ml-2 font-semibold">{tariff.sessionCredit}€</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Roaming Matrix
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <span>{data.length} Contacts gefunden</span>
          <span>•</span>
          <span>{expandedItems.size} aufgeklappt</span>
          <button
            onClick={() => setExpandedItems(new Set(data.map(c => c.id)))}
            className="text-primary hover:underline"
          >
            Alle aufklappen
          </button>
          <button
            onClick={() => setExpandedItems(new Set())}
            className="text-primary hover:underline"
          >
            Alle zuklappen
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {data.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="text-gray-400 text-lg mb-2">🌐</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Keine Contacts in der Roaming Matrix
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Es wurden noch keine Contacts für die Roaming Matrix markiert.
              Bearbeiten Sie Contacts und setzen Sie das Flag "Sichtbar in Roaming Matrix".
            </p>
          </div>
        ) : (
          data.map((contact) => (
            <ContactItem key={contact.id} contact={contact} />
          ))
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;

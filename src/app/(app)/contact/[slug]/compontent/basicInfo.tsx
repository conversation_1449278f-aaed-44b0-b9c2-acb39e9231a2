"use client";

import React, { useContext, useEffect, useMemo, useState, useTransition } from "react";
import type { SubmitHandler } from "react-hook-form";
import { Controller, useForm } from "react-hook-form";
import FormContext from "./formContext";
import { useRouter } from "next/navigation";
import { ContactWithIncludes } from "../page";
import { InvoiceLanguageCode, InvoicePosition, Provider } from "@prisma/client";
import SelectOU from "~/app/(app)/users/compontent/SelectOu";
import { INode } from "~/app/(app)/tenantconfiguration/component/Tree";
import MultiSelect from "~/component/MultiSelect";
import { MultiValue } from "react-select";
import { FaCopy, FaCube } from "react-icons/fa6";
import { GiPerspectiveDiceSixFacesRandom } from "react-icons/gi";
import crypto from "crypto";
import Link from "next/link";

interface Props {
  contact: ContactWithIncludes;
  tenantsTree: INode[];
  unmappedProvider: Provider[];
}

const BasicInfo = ({ contact, tenantsTree, unmappedProvider }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [selectedOptions, allOptions] = useMemo(() => {
    const selectedOptions = contact?.providers?.map((provider) => {
      return {
        label: `${provider.providerCountryId}*${provider.providerId}`,
        value: `${provider.providerCountryId}*${provider.providerId}`,
      };
    });
    const unmappedOptions = unmappedProvider.map((provider) => {
      return {
        label: `${provider.providerCountryId}*${provider.providerId}`,
        value: `${provider.providerCountryId}*${provider.providerId}`,
      };
    });
    return [selectedOptions, [...selectedOptions, ...unmappedOptions]];
  }, [contact, unmappedProvider]);

  // providers ersetzt, damit es mit dem MultiSelect sauber definiert ist.
  // sonst ist onChange etc nicht so easy
  type FormValues = Omit<ContactWithIncludes, "providers"> & {
    providers: string[];
  };

  const copyText = async () => {
    if (contact.sepaHash && typeof window !== "undefined") {
      try {
        await navigator.clipboard.writeText(
          `${window?.location.origin}/createCompanySepa?sepaHash=${contact.sepaHash}`,
        );
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000); // Nach 2 Sekunden wird der Span wieder ausgeblendet
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    }
  };
  const {
    register,
    handleSubmit,
    setValue,
    formState: { isDirty },
    control,
    watch,
    reset,
  } = useForm<FormValues>({
    defaultValues:
      {
        ...contact,
        providers: contact.providers.map(
          (provider) => provider.providerCountryId + "*" + provider.providerId,
        ),
      } || {},
  });
  const cpo = watch("cpo");
  const sepaHashWatch = watch("sepaHash");
  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    // Mutate external data source
    const result = await fetch(`/api/contact`, {
      method: "POST",
      body: JSON.stringify({ contact: data }),
    });
    if (result.status == 200) {
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push("/contact");
        router.refresh();
      });
    }
  };

  const { edit } = useContext(FormContext);

  useEffect(() => {
    if (!edit) {
      reset();
    }
  }, [edit]);
  return (
    <div
      className="relative mt-6 flex min-w-0 flex-col break-words rounded-2xl border-0 bg-white bg-clip-border shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl"
      id="basic-info"
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-0 rounded-t-2xl p-6">
          <h5 id={"basic-data"} className="dark:text-white">
            Basis Daten
          </h5>
        </div>
        <div className="flex-auto p-6 pt-0">
          <div className="-mx-3 my-3 flex flex-wrap">
            <div className="w-4/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Name
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("name")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400  dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-4/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Firmen Name"
              >
                Firmenname
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("companyName")}
                  type="text"
                  className="block w-full  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Kundennummer"
              >
                Kundenummer
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("customerNumber")}
                  type="text"
                  className="block w-full appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-2/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Kundennummer"
              >
                Lieferantennummer
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("supplierNumber")}
                  type="text"
                  className="block w-full  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 my-3 flex flex-wrap">
            <div className="w-3/12 max-w-full flex-0 px-3">
              <label className="mb-2 ml-1 text-xs font-bold  dark:text-white/80" htmlFor="Name">
                Sprache für Kundenkommunikation
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <select
                  id={"language"}
                  disabled={!edit}
                  {...register("invoiceLanguageCode")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950"
                >
                  {Object.values(InvoiceLanguageCode).map((lang) => {
                    return (
                      <option key={lang} value={lang}>
                        {lang}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
            {cpo && (
              <div className="w-4/12 max-w-full flex-0 px-3">
                <label
                  className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="Name"
                >
                  Zugeordnete Organisationseinheit
                </label>
                <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                  <Controller
                    name="ouId" // Set the name to match the field name
                    control={control} // Pass the control object from useForm
                    render={({ field }) => (
                      <SelectOU
                        {...field}
                        defaultSelected={field.value ?? ""}
                        nodes={tenantsTree}
                        setObject={false}
                      />
                    )}
                  />
                </div>
              </div>
            )}
            <div className="w-4/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="sepaHash"
              >
                Einmaliger SEPA Einladungs-Hash
              </label>
              <div className="relative flex w-full flex-row rounded-lg">
                <input
                  id={"sepaHash"}
                  {...register("sepaHash")}
                  type="text"
                  disabled={!edit}
                  placeholder={""}
                  className="block w-full appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
                <GiPerspectiveDiceSixFacesRandom
                  className={`${!edit ? "bg-gray-500" : ""} rounded-2xl`}
                  title={"Hash generieren"}
                  onClick={() => {
                    if (edit) {
                      setValue(
                        "sepaHash",
                        crypto
                          .createHash("sha256")
                          .update(crypto.randomBytes(32).toString("hex"))
                          .digest("hex"),
                        { shouldDirty: true },
                      );
                    }
                  }}
                  size={30}
                />
              </div>
            </div>
            <div className="w-1/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Is CPO
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("cpo")}
                  className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3 disabled:bg-gray-400"
                  type="checkbox"
                />
              </div>
            </div>
          </div>
          {!cpo && (
            <div className="-mx-3 flex flex-wrap">
              <div className="w-6/12 max-w-full flex-0 px-3">
                <label
                  className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="empIds"
                >
                  EMP Ids
                </label>
                <Controller
                  name="providers" // Der Name des Feldes in den Formulardaten
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      isDisabled={!edit}
                      onChange={(val: MultiValue<any>) => field.onChange(val.map((v) => v.value))}
                      value={allOptions.filter((option) => field.value.includes(option.value))}
                      options={allOptions}

                      // Falls nötig, zusätzliche Props für das MultiSelect
                    />
                  )}
                />
              </div>
              <div className="w-6/12 max-w-full flex-0 px-3">
                <label
                  className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                  htmlFor="iban"
                >
                  IBAN
                </label>
                <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                  <input
                    {...register("iban")}
                    type="text"
                    disabled={!edit}
                    id={"iban"}
                    placeholder={""}
                    className="block w-full appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                  />
                </div>
              </div>
            </div>
          )}

          <div className="-mx-3 flex flex-wrap">
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="invoiceMail"
              >
                Rechnung E-Mail
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("invoiceMail")}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="cdrMail"
              >
                CDR E-Mail
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("cdrMail")}
                  type="text"
                  className="block  w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-12/12 w-full max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Notizen
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <textarea
                  disabled={!edit}
                  rows={3}
                  {...register("note")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none disabled:bg-gray-400 dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="noRoaming"
              >
                Kein Roaming
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("noRoaming")}
                  id="noRoaming"
                  className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3 disabled:bg-gray-400"
                  type="checkbox"
                />
              </div>
            </div>
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="visibleInRoamingMatrix"
              >
                Sichtbar in Roaming Matrix
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  disabled={!edit}
                  {...register("visibleInRoamingMatrix")}
                  id="visibleInRoamingMatrix"
                  className="relative float-left mt-0.5 h-5 w-10 cursor-pointer appearance-none rounded-10 border border-solid border-gray-200 bg-slate-800/10 bg-none bg-contain bg-left bg-no-repeat align-top transition-all duration-250 ease-soft-in-out after:absolute after:top-px after:h-4 after:w-4 after:translate-x-px after:rounded-circle after:bg-white after:shadow-soft-2xl after:duration-250 after:content-[''] checked:border-slate-800/95 checked:bg-slate-800/95 checked:bg-none checked:bg-right checked:after:translate-x-5.3 disabled:bg-gray-400"
                  type="checkbox"
                />
              </div>
            </div>
          </div>
          {isDirty && (
            <div className="-mx-3 flex">
              <div className={"flex-3 w-full max-w-full px-3"}>
                <button
                  type={"submit"}
                  className="float-right mb-0 mt-16 inline-block cursor-pointer rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
                >
                  Änderungen Speichern
                </button>
              </div>
            </div>
          )}
        </div>
      </form>
      {sepaHashWatch !== contact.sepaHash && (
        <h5 className={"p-6"}>SEPA Einladungslink: -SEPA Hash muss zunächst gespeichert werden-</h5>
      )}
      {cpo && sepaHashWatch == contact.sepaHash && (
        <div className={"flex flex-row p-6 text-xl"}>
          {contact.sepaHash && (
            <div className={"flex flex-row items-center"}>
              <Link
                target="_blank"
                href={`/createCompanySepa?sepaHash=${contact.sepaHash}`}
                className={"text-xl text-blue-500"}
              >
                SEPA Einladungslink
              </Link>
              <FaCopy className={"ml-4 text-primary hover:scale-110"} onClick={() => copyText()} />
              <span
                className={`ml-2 text-primary transition-opacity duration-500 ${
                  isCopied ? "opacity-100" : "opacity-0"
                }`}
              >
                Link kopiert!
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BasicInfo;

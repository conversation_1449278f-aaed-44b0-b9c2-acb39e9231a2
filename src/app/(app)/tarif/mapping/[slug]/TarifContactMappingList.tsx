"use client";
import { CreditTarif, Tarif } from "@prisma/client";
import { useRouter } from "next/navigation";
import React, { useMemo, useTransition, useState, useEffect } from "react";
import type { ContactsWithIncludes } from "./page";
import Table from "~/utils/table/table";
import type { ColDef, ColGroupDef } from "ag-grid-community";
import type { ICellRendererParams } from "ag-grid-community";
import Button from "~/component/button";
import { FiLoader, FiUsers, FiUserCheck, FiSearch } from "react-icons/fi";
import { BsToggleOn, BsToggleOff } from "react-icons/bs";
import { isTariffValidForOu, canAssignContactToTariff } from "~/utils/tariff-validation";

interface Props {
  tarif: Tarif | CreditTarif;
  contacts: ContactsWithIncludes[];
}

const TarifContactMappingList = ({ tarif, contacts }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [searchTerm, setSearchTerm] = useState("");
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [operatorOus, setOperatorOus] = useState<any[]>([]);

  // Load valid OUs for this tariff
  useEffect(() => {
    const loadValidOus = async () => {
      const standardTarif = tarif as any;
      if (standardTarif.validOus && standardTarif.validOus.length > 0) {
        setOperatorOus(standardTarif.validOus);
      } else {
        // If no specific OUs are set, load all OUs
        try {
          const response = await fetch(`/api/admin/ous`);
          if (response.ok) {
            const ous = await response.json();
            setOperatorOus(ous);
          }
        } catch (error) {
          console.error("Error loading OUs:", error);
        }
      }
    };

    void loadValidOus();
  }, [tarif]);

  const filteredContacts = useMemo(() => {
    let filtered;
    if ("energyCredit" in tarif) {
      filtered = contacts.filter((contact) => contact.cpo);
    } else {
      filtered = contacts.filter((contact) => !contact.cpo);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((contact) =>
        contact.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.customerNumber?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [contacts, tarif, searchTerm]);

  // Helper function to check if contact is mapped to tariff
  const isMapped = (contact: ContactsWithIncludes, tarif: Tarif | CreditTarif) => {
    if ("energyCredit" in tarif) {
      return !!contact.creditTarifs.find((contactTarif) => contactTarif.creditTarifId == tarif.id);
    } else {
      return !!contact.tarifs.find((contactTarif) => contactTarif.tarifId == tarif.id);
    }
  };

  // Get provider IDs for mapped contacts
  const mappedProviderIds = useMemo(() => {
    const mappedContacts = filteredContacts.filter(contact => isMapped(contact, tarif));
    const providerIds = new Set<string>();

    mappedContacts.forEach(contact => {
      if (contact.providers && contact.providers.length > 0) {
        contact.providers.forEach(provider => {
          if (provider.providerId) {
            const providerId = provider.providerId;
            const countryId = provider.providerCountryId || 'DE';
            providerIds.add(`${providerId}-${countryId}`);
          }
        });
      }
    });

    return Array.from(providerIds).sort();
  }, [filteredContacts, tarif]);

  const mapTarifToContact = (contact: ContactsWithIncludes, map: boolean) => {
    startTransition(async () => {
      // Determine the correct API endpoint based on tariff type
      const apiEndpoint = "energyCredit" in tarif
        ? "/api/contact/mapCreditTarif"
        : "/api/contact/mapRoamingTarif";

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contact: contact,
          tarif: tarif,
          subscribed: map,
        }),
      });

      if (response.ok) {
        router.refresh();
      }
    });
  };

  // Statistics
  const mappedCount = filteredContacts.filter(contact => isMapped(contact, tarif)).length;
  const totalCount = filteredContacts.length;

  // Bulk actions
  const handleBulkAction = async (mapAll: boolean) => {
    setBulkActionLoading(true);
    try {
      const promises = filteredContacts
        .filter(contact => isMapped(contact, tarif) !== mapAll)
        .map(contact => mapTarifToContact(contact, mapAll));

      await Promise.all(promises);
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Status Cell Renderer
  const StatusCellRenderer = (params: ICellRendererParams) => {
    const contact = params.data;
    const mapped = isMapped(contact, tarif);

    return (
      <div className="flex items-center justify-center">
        {mapped ? (
          <BsToggleOn
            className="cursor-pointer text-emerald-500 hover:text-emerald-600 text-xl"
            onClick={() => mapTarifToContact(contact, false)}
            title="Zugeordnet - Klicken zum Entfernen"
          />
        ) : (
          <BsToggleOff
            className="cursor-pointer text-gray-400 hover:text-gray-600 text-xl"
            onClick={() => mapTarifToContact(contact, true)}
            title="Nicht zugeordnet - Klicken zum Zuordnen"
          />
        )}
      </div>
    );
  };

  // Compact Tariff Display Component
  const TariffDisplay = () => {
    const isCredit = "energyCredit" in tarif;
    const standardTarif = tarif as any; // Type assertion for easier access

    return (
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {standardTarif.name || "Unbenannter Tarif"}
            </h3>
            <div className="flex gap-1 mt-1">
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                isCredit
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  : 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'
              }`}>
                {isCredit ? 'Credit' : 'Standard'}
              </span>
              {standardTarif.kindOfTarif && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                  {standardTarif.kindOfTarif}
                </span>
              )}

            </div>
          </div>
        </div>

        {/* Compact Information Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 text-sm">
          {/* Helper function to get card styling based on value */}
          {(() => {
            const getCardStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                : "bg-gray-50 dark:bg-gray-700";
            };

            const getValueStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "font-medium text-gray-400 dark:text-gray-500 italic"
                : "font-semibold text-gray-900 dark:text-white";
            };

            const formatValue = (value: any, suffix = "", isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty ? "nicht gesetzt" : `${value}${suffix}`;
            };

            return (
              <>
                {/* Pricing */}
                {isCredit ? (
                  <>
                    <div className={`${getCardStyle(standardTarif.sessionCredit)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Session Credit</p>
                      <p className={getValueStyle(standardTarif.sessionCredit)}>
                        {standardTarif.sessionCredit ?
                          `${(standardTarif.sessionCredit).toFixed(2)} € ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.sessionCredit && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.energyCredit)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Energy Credit</p>
                      <p className={getValueStyle(standardTarif.energyCredit)}>
                        {standardTarif.energyCredit ?
                          `${(standardTarif.energyCredit).toFixed(2)} €/kWh ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.energyCredit && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.blockingCredit)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Blocking Credit</p>
                      <p className={getValueStyle(standardTarif.blockingCredit)}>
                        {standardTarif.blockingCredit ?
                          `${(standardTarif.blockingCredit).toFixed(2)} €/min ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.blockingCredit && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.maxBlockingCredit)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Max Blocking</p>
                      <p className={getValueStyle(standardTarif.maxBlockingCredit)}>
                        {standardTarif.maxBlockingCredit ?
                          `${(standardTarif.maxBlockingCredit).toFixed(2)} € ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.maxBlockingCredit && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className={`${getCardStyle(standardTarif.sessionFee)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Session Fee</p>
                      <p className={getValueStyle(standardTarif.sessionFee)}>
                        {standardTarif.sessionFee ?
                          `${(standardTarif.sessionFee).toFixed(2)} € ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.sessionFee && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.kwh)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">kWh Price</p>
                      <p className={getValueStyle(standardTarif.kwh)}>
                        {standardTarif.kwh ?
                          `${(standardTarif.kwh).toFixed(2)} €/kWh ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.kwh && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.blockingFee)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Blocking Fee</p>
                      <p className={getValueStyle(standardTarif.blockingFee)}>
                        {standardTarif.blockingFee ?
                          `${(standardTarif.blockingFee).toFixed(2)} €/min ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.blockingFee && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.blockingFeeBeginAtMin)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Blocking ab</p>
                      <p className={getValueStyle(standardTarif.blockingFeeBeginAtMin)}>
                        {standardTarif.blockingFeeBeginAtMin ?
                          `${standardTarif.blockingFeeBeginAtMin} min` :
                          "nicht gesetzt"
                        }
                      </p>
                    </div>
                    <div className={`${getCardStyle(standardTarif.blockingFeeMax)} p-2 rounded`}>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Max Blocking</p>
                      <p className={getValueStyle(standardTarif.blockingFeeMax)}>
                        {standardTarif.blockingFeeMax ?
                          `${(standardTarif.blockingFeeMax).toFixed(2)} € ` :
                          "nicht gesetzt"
                        }
                        {standardTarif.blockingFeeMax && <span className="text-xs text-gray-500">netto</span>}
                      </p>
                    </div>
                  </>
                )}
              </>
            );
          })()}

          {/* Technical Details */}
          <div className={`${(() => {
            const getCardStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                : "bg-gray-50 dark:bg-gray-700";
            };
            return getCardStyle(standardTarif.currentType, true);
          })()} p-2 rounded`}>
            <p className="text-xs text-gray-600 dark:text-gray-400">Ladepunkt-Typ</p>
            <p className={`${standardTarif.currentType ?
              "font-semibold text-gray-900 dark:text-white" :
              "font-medium text-gray-400 dark:text-gray-500 italic"
            }`}>
              {standardTarif.currentType || "nicht gesetzt"}
            </p>
          </div>

          {!isCredit && (
            <div className={`${(() => {
              const getCardStyle = (value: any) => {
                const isEmpty = (!value || value === 0);
                return isEmpty
                  ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                  : "bg-gray-50 dark:bg-gray-700";
              };
              return getCardStyle(standardTarif.minChargingTime);
            })()} p-2 rounded`}>
              <p className="text-xs text-gray-600 dark:text-gray-400">Min. Ladezeit</p>
              <p className={`${standardTarif.minChargingTime ?
                "font-semibold text-gray-900 dark:text-white" :
                "font-medium text-gray-400 dark:text-gray-500 italic"
              }`}>
                {standardTarif.minChargingTime ?
                  `${(standardTarif.minChargingTime).toFixed(0)} s` :
                  "nicht gesetzt"
                }
              </p>
            </div>
          )}

          {!isCredit && (
            <div className={`${(() => {
              const getCardStyle = (value: any) => {
                const isEmpty = (!value || value === 0);
                return isEmpty
                  ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                  : "bg-gray-50 dark:bg-gray-700";
              };
              return getCardStyle(standardTarif.minChargingEnergy);
            })()} p-2 rounded`}>
              <p className="text-xs text-gray-600 dark:text-gray-400">Min. Lademenge</p>
              <p className={`${standardTarif.minChargingEnergy ?
                "font-semibold text-gray-900 dark:text-white" :
                "font-medium text-gray-400 dark:text-gray-500 italic"
              }`}>
                {standardTarif.minChargingEnergy ?
                  `${(standardTarif.minChargingEnergy).toFixed(1)} kWh` :
                  "nicht gesetzt"
                }
              </p>
            </div>
          )}

          {isCredit && (
            <div className={`${(() => {
              const getCardStyle = (value: any, isString = false) => {
                const isEmpty = isString ? !value : (!value || value === 0);
                return isEmpty
                  ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                  : "bg-gray-50 dark:bg-gray-700";
              };
              return getCardStyle(standardTarif.tarifType, true);
            })()} p-2 rounded`}>
              <p className="text-xs text-gray-600 dark:text-gray-400">Tarif Typ</p>
              <p className={`${standardTarif.tarifType ?
                "font-semibold text-gray-900 dark:text-white" :
                "font-medium text-gray-400 dark:text-gray-500 italic"
              }`}>
                {standardTarif.tarifType || "nicht gesetzt"}
              </p>
            </div>
          )}

          {isCredit && (
            <div className={`${(() => {
              const getCardStyle = (value: any, isString = false) => {
                const isEmpty = isString ? !value : (!value || value === 0);
                return isEmpty
                  ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                  : "bg-gray-50 dark:bg-gray-700";
              };
              return getCardStyle(standardTarif.powerType, true);
            })()} p-2 rounded`}>
              <p className="text-xs text-gray-600 dark:text-gray-400">Power Typ</p>
              <p className={`${standardTarif.powerType ?
                "font-semibold text-gray-900 dark:text-white" :
                "font-medium text-gray-400 dark:text-gray-500 italic"
              }`}>
                {standardTarif.powerType || "nicht gesetzt"}
              </p>
            </div>
          )}

          {isCredit && (
            <div className={`${(() => {
              const getCardStyle = (value: any) => {
                const isEmpty = (!value || value === 0);
                return isEmpty
                  ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                  : "bg-gray-50 dark:bg-gray-700";
              };
              return getCardStyle(standardTarif.blockingFeeMinStart);
            })()} p-2 rounded`}>
              <p className="text-xs text-gray-600 dark:text-gray-400">Blocking ab</p>
              <p className={`${standardTarif.blockingFeeMinStart ?
                "font-semibold text-gray-900 dark:text-white" :
                "font-medium text-gray-400 dark:text-gray-500 italic"
              }`}>
                {standardTarif.blockingFeeMinStart ?
                  `${standardTarif.blockingFeeMinStart} min` :
                  "nicht gesetzt"
                }
              </p>
            </div>
          )}

          {/* Validity */}
          <div className={`${(() => {
            const getCardStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                : "bg-gray-50 dark:bg-gray-700";
            };
            return getCardStyle(standardTarif.validFrom, true);
          })()} p-2 rounded`}>
            <p className="text-xs text-gray-600 dark:text-gray-400">Gültig ab</p>
            <p className={`${standardTarif.validFrom ?
              "font-semibold text-gray-900 dark:text-white" :
              "font-medium text-gray-400 dark:text-gray-500 italic"
            }`}>
              {standardTarif.validFrom ?
                new Date(standardTarif.validFrom).toLocaleDateString('de-DE') :
                "nicht gesetzt"
              }
            </p>
          </div>

          <div className={`${(() => {
            const getCardStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                : "bg-gray-50 dark:bg-gray-700";
            };
            return getCardStyle(standardTarif.validTo, true);
          })()} p-2 rounded`}>
            <p className="text-xs text-gray-600 dark:text-gray-400">Gültig bis</p>
            <p className={`${standardTarif.validTo ?
              "font-semibold text-gray-900 dark:text-white" :
              "font-medium text-gray-400 dark:text-gray-500 italic"
            }`}>
              {standardTarif.validTo ?
                new Date(standardTarif.validTo).toLocaleDateString('de-DE') :
                "nicht gesetzt"
              }
            </p>
          </div>

          <div className={`${(() => {
            const getCardStyle = (value: any, isString = false) => {
              const isEmpty = isString ? !value : (!value || value === 0);
              return isEmpty
                ? "bg-gray-100 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-600 opacity-60"
                : "bg-gray-50 dark:bg-gray-700";
            };
            return getCardStyle(standardTarif.contractId, true);
          })()} p-2 rounded`}>
            <p className="text-xs text-gray-600 dark:text-gray-400">Contract ID</p>
            <p className={`${standardTarif.contractId ?
              "font-semibold text-gray-900 dark:text-white text-xs font-mono" :
              "font-medium text-gray-400 dark:text-gray-500 italic"
            }`}>
              {standardTarif.contractId ?
                `${standardTarif.contractId.substring(0, 8)}...` :
                "nicht gesetzt"
              }
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-2 rounded">
            <p className="text-xs text-gray-600 dark:text-gray-400">Kontakte</p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {standardTarif._count?.contacts || 0}
            </p>
          </div>
        </div>

        {/* Valid OUs Section */}
        {operatorOus.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Gültige Organisationseinheiten
            </h5>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-3">
                <div className="flex-1">
                  <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">
                    {standardTarif.validOus && standardTarif.validOus.length > 0
                      ? `Dieser Tarif ist nur für ${operatorOus.length} spezifische OU(s) gültig`
                      : `Dieser Tarif ist für alle Organisationseinheiten gültig (${operatorOus.length} OUs)`
                    }
                  </p>
                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                    Nur Kontakte aus diesen Organisationseinheiten können diesem Tarif zugeordnet werden
                  </p>
                </div>
              </div>

              {/* Valid OUs */}
              <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
                <p className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2">
                  {standardTarif.validOus && standardTarif.validOus.length > 0
                    ? `Gültige Organisationseinheiten (${operatorOus.length}):`
                    : `Alle Organisationseinheiten (${operatorOus.length}):`
                  }
                </p>
                <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                  {operatorOus.map((ou) => (
                    <span
                      key={ou.id}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200"
                      title={`${ou.name} (${ou.code})`}
                    >
                      {ou.code}
                    </span>
                  ))}
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  Hover über OU-Code für vollständigen Namen
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Provider IDs Section */}
        {mappedProviderIds.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Zugeordnete Provider IDs ({mappedProviderIds.length})
            </h5>
            <div className="flex flex-wrap gap-2">
              {mappedProviderIds.map((providerId) => (
                <span
                  key={providerId}
                  className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                >
                  {providerId}
                </span>
              ))}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Diese Provider IDs sind den aktuell zugeordneten Kontakten zugewiesen
            </p>
          </div>
        )}

        {mappedProviderIds.length === 0 && mappedCount > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Provider IDs
            </h5>
            <p className="text-xs text-gray-500 dark:text-gray-400 italic">
              Keine Provider IDs bei den zugeordneten Kontakten gefunden
            </p>
          </div>
        )}

        {/* Additional Information */}
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
          <div className="text-xs text-gray-600 dark:text-gray-400">
            <span className="font-medium">Tarif ID:</span>
            <span className="ml-1 font-mono">{standardTarif.id}</span>
          </div>
        </div>
      </div>
    );
  };

  // Validation Cell Renderer
  const ValidationCellRenderer = (params: ICellRendererParams) => {
    const contact = params.data;
    const isValid = canAssignContactToTariff(contact, tarif);

    if (isValid) {
      return (
        <div className="flex items-center justify-center">
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            ✓ Gültig
          </span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center justify-center">
          <span
            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
            title={`OU "${contact.ou?.name || 'Keine OU'}" (${contact.ou?.operatorId || 'Keine Operator ID'}) ist nicht kompatibel mit Tarif Operator ID "${(tarif as any).operatorId}"`}
          >
            ✗ Ungültig
          </span>
        </div>
      );
    }
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 2,
      pinned: "left"
    },
    {
      field: "companyName",
      headerName: "Unternehmen",
      flex: 2,
      valueFormatter: (params) => params.value || "-"
    },
    {
      field: "customerNumber",
      headerName: "Kundennummer",
      flex: 1,
      valueFormatter: (params) => params.value || "-"
    },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      cellRenderer: StatusCellRenderer,
      sortable: false,
      filter: false,
      pinned: "right"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Tariff Display */}
      <TariffDisplay />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <FiUsers className="text-blue-500 mr-3" size={20} />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Verfügbare Kontakte</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <FiUserCheck className="text-emerald-500 mr-3" size={20} />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Zugeordnet</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{mappedCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-300 rounded mr-3"></div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Nicht zugeordnet</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalCount - mappedCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Bulk Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Kontakte durchsuchen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() => handleBulkAction(true)}
            disabled={bulkActionLoading || mappedCount === totalCount}
            className="bg-emerald-500 hover:bg-emerald-600 text-white text-sm px-3 py-2"
          >
            {bulkActionLoading ? <FiLoader className="animate-spin mr-1" /> : null}
            Alle zuordnen
          </Button>
          <Button
            onClick={() => handleBulkAction(false)}
            disabled={bulkActionLoading || mappedCount === 0}
            className="bg-slate-500 hover:bg-slate-600 text-white text-sm px-3 py-2"
          >
            {bulkActionLoading ? <FiLoader className="animate-spin mr-1" /> : null}
            Alle entfernen
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="h-[60vh] min-h-[400px]">
        <Table
          gridId="tarif-contact-mapping"
          columnDefs={columnDefs}
          rowData={filteredContacts}
          overlayNoRowsTemplate={
            searchTerm
              ? `Keine Kontakte gefunden für "${searchTerm}"`
              : "Keine Kontakte verfügbar"
          }
        />
      </div>
    </div>
  );
};

export default TarifContactMappingList;

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testRoamingMatrixFlag() {
  try {
    console.log('Testing visibleInRoamingMatrix flag...');

    // Get first 3 contacts
    const contacts = await prisma.contact.findMany({
      take: 3,
      include: {
        tarifs: true,
      },
    });

    if (contacts.length === 0) {
      console.log('No contacts found in database');
      return;
    }

    console.log(`Found ${contacts.length} contacts to update`);

    // Update all contacts to set the flag
    for (const contact of contacts) {
      const updatedContact = await prisma.contact.update({
        where: { id: contact.id },
        data: { visibleInRoamingMatrix: true },
      });

      console.log(`Updated contact: ${contact.name || contact.companyName || contact.id} - visibleInRoamingMatrix: ${updatedContact.visibleInRoamingMatrix} (${contact.tarifs.length} tariffs)`);
    }

    // Test the roaming matrix API query
    const roamingMatrixContacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        tarifs: {
          include: {
            tarif: {
              include: {
                validOus: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
              },
            },
          },
        },
        creditTarifs: {
          include: {
            creditTarif: true,
          },
        },
      },
    });

    console.log(`Found ${roamingMatrixContacts.length} contacts with visibleInRoamingMatrix=true`);
    
    roamingMatrixContacts.forEach(contact => {
      console.log(`- ${contact.name || contact.companyName || contact.id} (${contact.tarifs.length} tariffs)`);
    });

  } catch (error) {
    console.error('Error testing roaming matrix flag:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRoamingMatrixFlag();

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeSimultaneousTariffs() {
  try {
    console.log('Analyzing tariffs that are simultaneously valid (ignoring 1-day overlaps)...\n');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get contacts with visibleInRoamingMatrix flag
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        tarifs: {
          include: {
            tarif: true,
          },
        },
      },
    });

    console.log(`Analyzing ${contacts.length} contacts for simultaneous tariffs...\n`);

    let simultaneousCases = 0;

    for (const contact of contacts) {
      const allTariffs = contact.tarifs.map(t => t.tarif);
      const validTariffs = allTariffs.filter(tarif => {
        const validFrom = new Date(tarif.validFrom);
        const validTo = new Date(tarif.validTo);
        return validFrom <= today && validTo >= today;
      });

      // Group by currentType
      const acTariffs = validTariffs.filter(t => t.currentType === "AC");
      const dcTariffs = validTariffs.filter(t => t.currentType === "DC");

      // Check for simultaneous AC tariffs (overlap > 1 day)
      if (acTariffs.length > 1) {
        let hasSignificantOverlap = false;
        
        for (let i = 0; i < acTariffs.length; i++) {
          for (let j = i + 1; j < acTariffs.length; j++) {
            const tariff1 = acTariffs[i];
            const tariff2 = acTariffs[j];
            
            const start1 = new Date(tariff1.validFrom);
            const end1 = new Date(tariff1.validTo);
            const start2 = new Date(tariff2.validFrom);
            const end2 = new Date(tariff2.validTo);

            // Calculate overlap
            const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
            const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));
            
            if (overlapStart <= overlapEnd) {
              const overlapDays = Math.ceil((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
              
              // Only consider significant overlaps (more than 1 day)
              if (overlapDays > 1) {
                hasSignificantOverlap = true;
                
                if (!simultaneousCases || simultaneousCases === 0) {
                  simultaneousCases++;
                  console.log(`🔴 SIMULTANEOUS TARIFFS - Contact: ${contact.companyName || contact.name}`);
                  console.log(`   AC Tariffs simultaneously valid (overlap > 1 day):`);
                }
                
                console.log(`   📅 "${tariff1.name}" ↔ "${tariff2.name}"`);
                console.log(`      Tariff 1: ${start1.toISOString().split('T')[0]} → ${end1.toISOString().split('T')[0]} (${tariff1.kwh}€/kWh + ${tariff1.sessionFee}€/session)`);
                console.log(`      Tariff 2: ${start2.toISOString().split('T')[0]} → ${end2.toISOString().split('T')[0]} (${tariff2.kwh}€/kWh + ${tariff2.sessionFee}€/session)`);
                console.log(`      Overlap: ${overlapStart.toISOString().split('T')[0]} → ${overlapEnd.toISOString().split('T')[0]} (${overlapDays} days)`);
                
                // Check if today is in overlap period
                if (today >= overlapStart && today <= overlapEnd) {
                  console.log(`      ⚠️  TODAY (${today.toISOString().split('T')[0]}) is in overlap period!`);
                  
                  // Show which tariff would be selected
                  const selectedTariff = new Date(tariff1.validFrom) > new Date(tariff2.validFrom) ? tariff1 : tariff2;
                  console.log(`      ✅ Selected: "${selectedTariff.name}" (most recent validFrom)`);
                }
                console.log('');
              }
            }
          }
        }
        
        if (hasSignificantOverlap) {
          simultaneousCases++;
        }
      }

      // Check for simultaneous DC tariffs (overlap > 1 day)
      if (dcTariffs.length > 1) {
        let hasSignificantOverlap = false;
        
        for (let i = 0; i < dcTariffs.length; i++) {
          for (let j = i + 1; j < dcTariffs.length; j++) {
            const tariff1 = dcTariffs[i];
            const tariff2 = dcTariffs[j];
            
            const start1 = new Date(tariff1.validFrom);
            const end1 = new Date(tariff1.validTo);
            const start2 = new Date(tariff2.validFrom);
            const end2 = new Date(tariff2.validTo);

            // Calculate overlap
            const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
            const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));
            
            if (overlapStart <= overlapEnd) {
              const overlapDays = Math.ceil((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
              
              // Only consider significant overlaps (more than 1 day)
              if (overlapDays > 1) {
                hasSignificantOverlap = true;
                
                if (!simultaneousCases || simultaneousCases === 0) {
                  simultaneousCases++;
                  console.log(`🔴 SIMULTANEOUS TARIFFS - Contact: ${contact.companyName || contact.name}`);
                  console.log(`   DC Tariffs simultaneously valid (overlap > 1 day):`);
                }
                
                console.log(`   📅 "${tariff1.name}" ↔ "${tariff2.name}"`);
                console.log(`      Tariff 1: ${start1.toISOString().split('T')[0]} → ${end1.toISOString().split('T')[0]} (${tariff1.kwh}€/kWh + ${tariff1.sessionFee}€/session)`);
                console.log(`      Tariff 2: ${start2.toISOString().split('T')[0]} → ${end2.toISOString().split('T')[0]} (${tariff2.kwh}€/kWh + ${tariff2.sessionFee}€/session)`);
                console.log(`      Overlap: ${overlapStart.toISOString().split('T')[0]} → ${overlapEnd.toISOString().split('T')[0]} (${overlapDays} days)`);
                
                // Check if today is in overlap period
                if (today >= overlapStart && today <= overlapEnd) {
                  console.log(`      ⚠️  TODAY (${today.toISOString().split('T')[0]}) is in overlap period!`);
                  
                  // Show which tariff would be selected
                  const selectedTariff = new Date(tariff1.validFrom) > new Date(tariff2.validFrom) ? tariff1 : tariff2;
                  console.log(`      ✅ Selected: "${selectedTariff.name}" (most recent validFrom)`);
                }
                console.log('');
              }
            }
          }
        }
        
        if (hasSignificantOverlap) {
          simultaneousCases++;
        }
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`   Total contacts analyzed: ${contacts.length}`);
    console.log(`   Contacts with simultaneous tariffs (>1 day overlap): ${simultaneousCases}`);
    console.log(`   Today: ${today.toISOString().split('T')[0]}`);
    
    if (simultaneousCases === 0) {
      console.log(`   ✅ No problematic simultaneous tariffs found!`);
      console.log(`   ✅ All overlaps are ≤1 day (acceptable transition periods)`);
    }

  } catch (error) {
    console.error('Error analyzing simultaneous tariffs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeSimultaneousTariffs();

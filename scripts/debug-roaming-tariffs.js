import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugRoamingTariffs() {
  try {
    console.log('Debugging Roaming Matrix tariffs...\n');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get contacts with visibleInRoamingMatrix flag
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        tarifs: {
          include: {
            tarif: true,
          },
        },
      },
    });

    console.log(`Found ${contacts.length} contacts with visibleInRoamingMatrix=true\n`);

    for (const contact of contacts) {
      console.log(`\n📋 Contact: ${contact.companyName || contact.name || contact.id}`);
      console.log(`   Total tariffs assigned: ${contact.tarifs.length}`);

      // Check all tariffs for this contact
      const allTariffs = contact.tarifs.map(t => t.tarif);
      const validTariffs = allTariffs.filter(tarif => {
        const validFrom = new Date(tarif.validFrom);
        const validTo = new Date(tarif.validTo);
        return validFrom <= today && validTo >= today;
      });

      console.log(`   Valid tariffs (today): ${validTariffs.length}`);

      // Group by currentType
      const acTariffs = validTariffs.filter(t => t.currentType === "AC");
      const dcTariffs = validTariffs.filter(t => t.currentType === "DC");

      console.log(`   ⚡ AC tariffs: ${acTariffs.length}`);
      acTariffs.forEach(tariff => {
        console.log(`      - ${tariff.name} (${tariff.kwh}€/kWh, ${tariff.sessionFee}€/session)`);
        console.log(`        Valid: ${tariff.validFrom.toISOString().split('T')[0]} - ${tariff.validTo.toISOString().split('T')[0]}`);
      });

      console.log(`   🔌 DC tariffs: ${dcTariffs.length}`);
      dcTariffs.forEach(tariff => {
        console.log(`      - ${tariff.name} (${tariff.kwh}€/kWh, ${tariff.sessionFee}€/session)`);
        console.log(`        Valid: ${tariff.validFrom.toISOString().split('T')[0]} - ${tariff.validTo.toISOString().split('T')[0]}`);
      });

      // Show what would be selected (most recent)
      if (acTariffs.length > 1) {
        const latestAC = acTariffs.sort((a, b) => new Date(b.validFrom).getTime() - new Date(a.validFrom).getTime())[0];
        console.log(`   ✅ Selected AC: ${latestAC.name} (most recent)`);
      }
      
      if (dcTariffs.length > 1) {
        const latestDC = dcTariffs.sort((a, b) => new Date(b.validFrom).getTime() - new Date(a.validFrom).getTime())[0];
        console.log(`   ✅ Selected DC: ${latestDC.name} (most recent)`);
      }
    }

  } catch (error) {
    console.error('Error debugging roaming tariffs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugRoamingTariffs();

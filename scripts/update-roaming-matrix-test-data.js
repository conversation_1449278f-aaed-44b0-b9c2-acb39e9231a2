const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateTestData() {
  try {
    console.log('Updating test data for Roaming Matrix...');

    // Get first 3 contacts to set as visible in roaming matrix
    const contacts = await prisma.contact.findMany({
      take: 3,
      include: {
        tarifs: true,
      },
    });

    console.log(`Found ${contacts.length} contacts to update`);

    for (const contact of contacts) {
      await prisma.contact.update({
        where: { id: contact.id },
        data: { visibleInRoamingMatrix: true },
      });
      
      console.log(`Updated contact: ${contact.name || contact.companyName || contact.id} - set visibleInRoamingMatrix to true`);
    }

    console.log('Test data update completed!');
    console.log('You can now view the Roaming Matrix with test data.');

  } catch (error) {
    console.error('Error updating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTestData();

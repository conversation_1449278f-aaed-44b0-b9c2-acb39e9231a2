import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeOverlappingTariffs() {
  try {
    console.log('Analyzing overlapping tariff validity periods...\n');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get contacts with visibleInRoamingMatrix flag
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        tarifs: {
          include: {
            tarif: true,
          },
        },
      },
    });

    console.log(`Analyzing ${contacts.length} contacts...\n`);

    let overlappingCases = 0;

    for (const contact of contacts) {
      const allTariffs = contact.tarifs.map(t => t.tarif);
      const validTariffs = allTariffs.filter(tarif => {
        const validFrom = new Date(tarif.validFrom);
        const validTo = new Date(tarif.validTo);
        return validFrom <= today && validTo >= today;
      });

      // Group by currentType
      const acTariffs = validTariffs.filter(t => t.currentType === "AC");
      const dcTariffs = validTariffs.filter(t => t.currentType === "DC");

      // Check for overlaps in AC tariffs
      if (acTariffs.length > 1) {
        overlappingCases++;
        console.log(`🔴 OVERLAP FOUND - Contact: ${contact.companyName || contact.name}`);
        console.log(`   AC Tariffs with overlapping validity:`);
        
        acTariffs.forEach((tariff, index) => {
          const validFrom = new Date(tariff.validFrom);
          const validTo = new Date(tariff.validTo);
          console.log(`   ${index + 1}. "${tariff.name}"`);
          console.log(`      Valid: ${validFrom.toISOString().split('T')[0]} → ${validTo.toISOString().split('T')[0]}`);
          console.log(`      Price: ${tariff.kwh}€/kWh + ${tariff.sessionFee}€/session`);
          console.log(`      Kind: ${tariff.kindOfTarif}`);
        });

        // Check exact overlap periods
        console.log(`   📅 Overlap Analysis:`);
        for (let i = 0; i < acTariffs.length; i++) {
          for (let j = i + 1; j < acTariffs.length; j++) {
            const tariff1 = acTariffs[i];
            const tariff2 = acTariffs[j];
            
            const start1 = new Date(tariff1.validFrom);
            const end1 = new Date(tariff1.validTo);
            const start2 = new Date(tariff2.validFrom);
            const end2 = new Date(tariff2.validTo);

            // Calculate overlap
            const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
            const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));
            
            if (overlapStart <= overlapEnd) {
              const overlapDays = Math.ceil((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
              console.log(`      "${tariff1.name}" ↔ "${tariff2.name}"`);
              console.log(`      Overlap: ${overlapStart.toISOString().split('T')[0]} → ${overlapEnd.toISOString().split('T')[0]} (${overlapDays} days)`);
              
              // Check if today is in overlap period
              if (today >= overlapStart && today <= overlapEnd) {
                console.log(`      ⚠️  TODAY (${today.toISOString().split('T')[0]}) is in overlap period!`);
              }
            }
          }
        }
        console.log('');
      }

      // Check for overlaps in DC tariffs
      if (dcTariffs.length > 1) {
        overlappingCases++;
        console.log(`🔴 OVERLAP FOUND - Contact: ${contact.companyName || contact.name}`);
        console.log(`   DC Tariffs with overlapping validity:`);
        
        dcTariffs.forEach((tariff, index) => {
          const validFrom = new Date(tariff.validFrom);
          const validTo = new Date(tariff.validTo);
          console.log(`   ${index + 1}. "${tariff.name}"`);
          console.log(`      Valid: ${validFrom.toISOString().split('T')[0]} → ${validTo.toISOString().split('T')[0]}`);
          console.log(`      Price: ${tariff.kwh}€/kWh + ${tariff.sessionFee}€/session`);
          console.log(`      Kind: ${tariff.kindOfTarif}`);
        });

        // Check exact overlap periods
        console.log(`   📅 Overlap Analysis:`);
        for (let i = 0; i < dcTariffs.length; i++) {
          for (let j = i + 1; j < dcTariffs.length; j++) {
            const tariff1 = dcTariffs[i];
            const tariff2 = dcTariffs[j];
            
            const start1 = new Date(tariff1.validFrom);
            const end1 = new Date(tariff1.validTo);
            const start2 = new Date(tariff2.validFrom);
            const end2 = new Date(tariff2.validTo);

            // Calculate overlap
            const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
            const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));
            
            if (overlapStart <= overlapEnd) {
              const overlapDays = Math.ceil((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
              console.log(`      "${tariff1.name}" ↔ "${tariff2.name}"`);
              console.log(`      Overlap: ${overlapStart.toISOString().split('T')[0]} → ${overlapEnd.toISOString().split('T')[0]} (${overlapDays} days)`);
              
              // Check if today is in overlap period
              if (today >= overlapStart && today <= overlapEnd) {
                console.log(`      ⚠️  TODAY (${today.toISOString().split('T')[0]}) is in overlap period!`);
              }
            }
          }
        }
        console.log('');
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`   Total contacts analyzed: ${contacts.length}`);
    console.log(`   Contacts with overlapping tariffs: ${overlappingCases}`);
    console.log(`   Today: ${today.toISOString().split('T')[0]}`);

  } catch (error) {
    console.error('Error analyzing overlapping tariffs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeOverlappingTariffs();

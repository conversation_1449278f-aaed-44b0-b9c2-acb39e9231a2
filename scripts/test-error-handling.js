import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testErrorHandling() {
  try {
    console.log('Testing new error handling for multiple tariffs...\n');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get contacts with visibleInRoamingMatrix flag
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        tarifs: {
          include: {
            tarif: {
              include: {
                validOus: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
              },
            },
          },
        },
        creditTarifs: {
          include: {
            creditTarif: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    console.log(`Processing ${contacts.length} contacts...\n`);

    // Process contacts to include only valid tariffs (same logic as API)
    const roamingMatrixData = contacts.map((contact) => {
      console.log(`Processing: ${contact.companyName || contact.name}`);
      
      // Filter valid roaming tariffs (Tarif model)
      const allValidRoamingTariffs = contact.tarifs
        .map((tarifOnContact) => tarifOnContact.tarif)
        .filter((tarif) => {
          const validFrom = new Date(tarif.validFrom);
          const validTo = new Date(tarif.validTo);
          return validFrom <= today && validTo >= today;
        });

      // Check for multiple valid tariffs per currentType and throw error
      const acTariffs = allValidRoamingTariffs.filter(t => t.currentType === "AC");
      const dcTariffs = allValidRoamingTariffs.filter(t => t.currentType === "DC");
      
      console.log(`  AC tariffs: ${acTariffs.length}, DC tariffs: ${dcTariffs.length}`);
      
      // Throw error if multiple tariffs exist for the same currentType
      if (acTariffs.length > 1) {
        const tariffNames = acTariffs.map(t => t.name).join(", ");
        throw new Error(`Contact "${contact.companyName || contact.name}" has multiple valid AC tariffs: ${tariffNames}. Please ensure only one tariff per currentType is valid at any time.`);
      }
      
      if (dcTariffs.length > 1) {
        const tariffNames = dcTariffs.map(t => t.name).join(", ");
        throw new Error(`Contact "${contact.companyName || contact.name}" has multiple valid DC tariffs: ${tariffNames}. Please ensure only one tariff per currentType is valid at any time.`);
      }

      // Use the single valid tariff per type
      const validRoamingTariffs = [...acTariffs, ...dcTariffs];

      // Filter valid credit tariffs (CreditTarif model)
      const validCreditTariffs = contact.creditTarifs
        .map((creditTarifOnContact) => creditTarifOnContact.creditTarif)
        .filter((creditTarif) => {
          const validFrom = new Date(creditTarif.validFrom);
          const validTo = creditTarif.validTo ? new Date(creditTarif.validTo) : null;
          return validFrom <= today && (!validTo || validTo >= today);
        });

      return {
        id: contact.id,
        name: contact.name,
        companyName: contact.companyName,
        customerNumber: contact.customerNumber,
        supplierNumber: contact.supplierNumber,
        cpo: contact.cpo,
        noRoaming: contact.noRoaming,
        ou: contact.ou,
        validRoamingTariffs: validRoamingTariffs.map((tarif) => ({
          id: tarif.id,
          name: tarif.name,
          currentType: tarif.currentType,
          kwh: tarif.kwh,
          sessionFee: tarif.sessionFee,
          kindOfTarif: tarif.kindOfTarif,
          validFrom: tarif.validFrom,
          validTo: tarif.validTo,
          validOus: tarif.validOus,
        })),
        validCreditTariffs: validCreditTariffs.map((creditTarif) => ({
          id: creditTarif.id,
          name: creditTarif.name,
          tarifType: creditTarif.tarifType,
          powerType: creditTarif.powerType,
          sessionCredit: creditTarif.sessionCredit,
          energyCredit: creditTarif.energyCredit,
          kindOfTarif: creditTarif.kindOfTarif,
          validFrom: creditTarif.validFrom,
          validTo: creditTarif.validTo,
        })),
      };
    });

    console.log(`\n✅ Success! Processed ${roamingMatrixData.length} contacts without errors.`);
    console.log('All contacts have at most 1 AC and 1 DC tariff.');

  } catch (error) {
    console.log(`\n🔴 ERROR CAUGHT (as expected):`);
    console.log(`   ${error.message}`);
    console.log('\nThis error indicates data integrity issues that need to be resolved.');
  } finally {
    await prisma.$disconnect();
  }
}

testErrorHandling();
